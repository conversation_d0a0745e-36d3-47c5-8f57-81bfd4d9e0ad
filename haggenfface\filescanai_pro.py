#!/usr/bin/env python3
"""
FileScanAI Pro - نظام مراقبة وتحليل الملفات الاحترافي
الإصدار: 2.5
المزايا الرئيسية:
- مراقبة تلقائية في الخلفية
- تكامل مع قواعد بيانات SQL وElasticsearch
- تحليل متقدم للملفات التنفيذية
- واجهة إدارة متكاملة
"""

import os
import hashlib
import asyncio
import sqlite3
import psutil
import lief
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from concurrent.futures import ThreadPoolExecutor
import pytesseract
from PIL import Image
import pandas as pd
import gradio as gr

# 1. ##################################################
#               إعدادات النظام الأساسية
# ##################################################
class Config:
    DATABASE_PATH = "filescanai.db"
    # Adjust paths for Windows
    MONITOR_DIRS = [
        os.path.join(os.path.expanduser('~'), 'Downloads'),
        os.getenv('TEMP', 'C:\Temp')
    ]
    QUARANTINE_DIR = os.path.join(os.getenv('ProgramData', 'C:\ProgramData'), 'FileScanAI', 'Quarantine')
    THRESHOLD_CPU = 80  # تحذير عند تجاوز استهلاك CPU
    THRESHOLD_MEMORY = 90  # تحذير عند تجاوز استهلاك الذاكرة

# 2. ##################################################
#               نظام قواعد البيانات
# ##################################################
class DatabaseManager:
    def __init__(self):
        self.conn = sqlite3.connect(Config.DATABASE_PATH, check_same_thread=False)
        self._init_db()
    
    def _init_db(self):
        cursor = self.conn.cursor()
        # جدول الأحداث الأمنية
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS security_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_path TEXT NOT NULL,
            file_hash TEXT NOT NULL,
            event_type TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            risk_score INTEGER,
            action_taken TEXT
        )
        """)
        
        # جدول أداء النظام
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS system_health (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cpu_usage REAL,
            memory_usage REAL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        self.conn.commit()

    def log_event(self, file_path, file_hash, event_type, risk_score, action):
        cursor = self.conn.cursor()
        cursor.execute("""
        INSERT INTO security_events 
        (file_path, file_hash, event_type, risk_score, action_taken)
        VALUES (?, ?, ?, ?, ?)
        """, (file_path, file_hash, event_type, risk_score, action))
        self.conn.commit()

    def log_system_health(self):
        cursor = self.conn.cursor()
        cpu = psutil.cpu_percent()
        memory = psutil.virtual_memory().percent
        cursor.execute("""
        INSERT INTO system_health (cpu_usage, memory_usage)
        VALUES (?, ?)
        """, (cpu, memory))
        self.conn.commit()
        return cpu, memory

# Handler for watchdog events
class FileEventHandler(FileSystemEventHandler):
    def __init__(self, core):
        self.core = core

    def on_created(self, event):
        if not event.is_directory:
            print(f"New file created: {event.src_path}. Submitting for analysis.")
            self.core.executor.submit(self.core.analyze_file, event.src_path)

    def on_modified(self, event):
        if not event.is_directory:
            print(f"File modified: {event.src_path}. Submitting for analysis.")
            self.core.executor.submit(self.core.analyze_file, event.src_path)

# 3. ##################################################
#              نواة المراقبة والتحليل
# ##################################################
class FileScanCore:
    def __init__(self):
        self.db = DatabaseManager()
        self.executor = ThreadPoolExecutor(max_workers=5)
        self.observer = None
    
    def start_monitoring(self):
        """بدء مراقبة المجلدات المحددة"""
        if self.observer and self.observer.is_alive():
            print("Monitoring is already running.")
            return

        event_handler = FileEventHandler(self)
        self.observer = Observer()
        
        for directory in Config.MONITOR_DIRS:
            if os.path.exists(directory):
                self.observer.schedule(event_handler, directory, recursive=True)
                print(f"Scheduled monitoring for: {directory}")
        
        if not self.observer.emitters:
            print("No valid directories to monitor.")
            return

        self.observer.start()
        print(f"Monitoring started on: {Config.MONITOR_DIRS}")
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        if self.observer and self.observer.is_alive():
            self.observer.stop()
            self.observer.join()
            print("Monitoring stopped.")
        else:
            print("Monitoring is not running.")
    
    def analyze_file(self, file_path):
        """تحليل الملف الشامل"""
        try:
            if not os.path.exists(file_path):
                return 0

            file_hash = self._calculate_hash(file_path)
            risk_score = 0
            
            # التحليل الأساسي
            file_ext = os.path.splitext(file_path)[1].lower()
            
            # كشف الملفات التنفيذية الخطرة
            if file_ext in ('.exe', '.dll', '.bat', '.vbs', '.ps1'):
                risk_score = self._analyze_executable(file_path)
            elif file_ext in ('.pdf', '.doc', '.docx'):
                risk_score = self._analyze_document(file_path)
            elif file_ext in ('.jpg', '.png', '.jpeg', '.bmp'):
                risk_score = self._analyze_image(file_path)
            
            # تسجيل الحدث
            action = "quarantine" if risk_score > 7 else "log"
            if action == "quarantine":
                self._quarantine_file(file_path, file_hash)
            
            self.db.log_event(file_path, file_hash, "file_analyzed", risk_score, action)
            return risk_score
            
        except Exception as e:
            print(f"Error analyzing file {file_path}: {e}")
            return 0

    def _calculate_hash(self, file_path):
        """حساب بصمة الملف"""
        hasher = hashlib.sha256()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except (IOError, PermissionError) as e:
            print(f"Could not hash file {file_path}: {e}")
            return None

    def _analyze_executable(self, file_path):
        """تحليل الملفات التنفيذية باستخدام LIEF"""
        try:
            binary = lief.parse(file_path)
            if not binary:
                return 0
                
            risk_factors = 0
            
            # تحليل الواردات المشبوهة
            suspicious_imports = {'CreateRemoteThread', 'WriteProcessMemory', 'VirtualAlloc'}
            imports = {e.name for e in binary.imports} if hasattr(binary, 'imports') else set()
            if imports & suspicious_imports:
                risk_factors += 5
            
            # تحليل الأقسام المشبوهة
            if hasattr(binary, 'sections') and any(section.name == '.malicious' for section in binary.sections):
                risk_factors += 8
                
            return risk_factors
            
        except Exception:
            return 3  # خطر متوسط إذا فشل التحليل

    def _analyze_image(self, file_path):
        """تحليل الصور للكشف عن النصوص"""
        try:
            text = pytesseract.image_to_string(Image.open(file_path))
            sensitive_keywords = ['سري', 'مباشر', 'خاص']
            return 7 if any(keyword in text for keyword in sensitive_keywords) else 1
        except Exception:
            return 1

    def _analyze_document(self, file_path):
        """تحليل الوثائق الأساسي"""
        return 3 if os.path.getsize(file_path) > 10_000_000 else 1  # ملفات كبيرة = مشبوهة

    def _quarantine_file(self, file_path, file_hash):
        """عزل الملف المشبوه"""
        try:
            os.makedirs(Config.QUARANTINE_DIR, exist_ok=True)
            dest = os.path.join(Config.QUARANTINE_DIR, f"{file_hash[:8]}_{os.path.basename(file_path)}")
            os.rename(file_path, dest)
            print(f"File quarantined: {file_path} -> {dest}")
        except Exception as e:
            print(f"Failed to quarantine file: {e}")

# 4. ##################################################
#             نظام مراقبة النظام الصحي
# ##################################################
class SystemHealthMonitor:
    def __init__(self):
        self.db = DatabaseManager()
        self._running = False
    
    async def start(self):
        """بدء مراقبة أداء النظام"""
        self._running = True
        while self._running:
            cpu, memory = self.db.log_system_health()
            
            if cpu > Config.THRESHOLD_CPU:
                print(f"Warning: High CPU usage ({cpu}%)")
            
            if memory > Config.THRESHOLD_MEMORY:
                print(f"Warning: High memory usage ({memory}%)")
            
            await asyncio.sleep(60)  # تحقق كل دقيقة
    
    def stop(self):
        """إيقاف المراقبة"""
        self._running = False

# 5. ##################################################
#               واجهة الإدارة
# ##################################################
class ManagementUI:
    def __init__(self, core):
        self.core = core
        self.health_monitor = SystemHealthMonitor()
        
    def run(self):
        """تشغيل واجهة المستخدم"""
        with gr.Blocks(title="نظام FileScanAI Pro") as app:
            with gr.Tab("لوحة التحكم"):
                gr.Markdown("## حالة النظام")
                with gr.Row():
                    cpu_usage = gr.Number(label="استهلاك CPU (%)")
                    mem_usage = gr.Number(label="استهلاك الذاكرة (%)")
                
                gr.Markdown("## الأحداث الأخيرة")
                events_table = gr.Dataframe(
                    self._get_recent_events, 
                    headers=["File Path", "Risk Score", "Timestamp"],
                    every=5
                )
                
            with gr.Tab("إدارة المراقبة"):
                monitor_status = gr.Textbox(label="حالة المراقبة", interactive=False, value="متوقفة")
                monitor_btn = gr.Button("بدء المراقبة")
                stop_btn = gr.Button("إيقاف المراقبة")
                
                gr.Markdown("## إعدادات المجلدات")
                dir_list = gr.DataFrame(
                    lambda: pd.DataFrame(Config.MONITOR_DIRS, columns=["المجلدات الخاضعة للمراقبة"]),
                    headers=["المجلدات الخاضعة للمراقبة"]
                )
                
            # أحداث الأزرار
            monitor_btn.click(
                self.start_monitoring_wrapper,
                outputs=[monitor_status]
            )
            
            stop_btn.click(
                self.stop_monitoring_wrapper,
                outputs=[monitor_status]
            )
            
            # تحديث النظام التلقائي
            app.load(
                self._update_system_stats,
                outputs=[cpu_usage, mem_usage],
                every=5
            )
            
        app.launch(server_name="0.0.0.0")

    def start_monitoring_wrapper(self):
        self.core.start_monitoring()
        return "تعمل"

    def stop_monitoring_wrapper(self):
        self.core.stop_monitoring()
        return "متوقفة"

    def _update_system_stats(self):
        cursor = self.core.db.conn.cursor()
        cursor.execute("SELECT cpu_usage, memory_usage FROM system_health ORDER BY timestamp DESC LIMIT 1")
        result = cursor.fetchone()
        return result if result else (0, 0)

    def _get_recent_events(self):
        cursor = self.core.db.conn.cursor()
        cursor.execute("""
        SELECT file_path, risk_score, timestamp 
        FROM security_events 
        ORDER BY timestamp DESC 
        LIMIT 10
        """)
        return cursor.fetchall()

# 6. ##################################################
#               التشغيل الرئيسي
# ##################################################
if __name__ == "__main__":
    # تهيئة النظام
    core = FileScanCore()
    ui = ManagementUI(core)
    
    # بدء الخدمات
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    main_thread = None
    
    try:
        # بدء مراقبة الصحة في الخلفية
        health_task = loop.create_task(ui.health_monitor.start())
        
        # تشغيل الواجهة (ستعمل في thread منفصل)
        import threading
        ui_thread = threading.Thread(target=ui.run, daemon=True)
        ui_thread.start()
        
        print("UI is running in a background thread.")
        print("System is running. Press Ctrl+C to stop.")
        
        # الحفاظ على التشغيل
        main_thread = loop.run_forever()
        
    except KeyboardInterrupt:
        print("Stopping system...")
    finally:
        ui.health_monitor.stop()
        core.stop_monitoring()
        if main_thread:
            loop.call_soon_threadsafe(loop.stop)
        loop.close()
        print("System shut down gracefully.")
